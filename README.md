# 商品管理系统 - 前端

这是一个基于Vue 3的商品管理系统前端应用，支持管理员和普通用户两种角色。

## 功能特性

### 登录系统
- 统一登录入口
- 根据用户名自动判断用户类型（admin为管理员，其他为普通用户）
- 登录成功后自动跳转到对应的界面

### 管理员端功能
- 数据统计概览（商品总数、销售额、库存预警、活跃用户）
- 快速操作（添加商品、库存盘点、生成报表、系统维护）
- 最近活动记录
- 侧边栏导航（首页概览、商品管理、库存管理、销售统计、用户管理、系统设置）

### 用户端功能
- 欢迎横幅
- 功能卡片（商品搜索、热门推荐、优惠活动、订单跟踪）
- 商品分类浏览
- 最新商品展示
- 侧边栏导航（首页、商品浏览、购物车、我的订单、个人中心）

## 技术栈

- Vue 3 (Composition API)
- Vue Router 4
- Vite
- 现代CSS (Grid, Flexbox, 渐变背景)

## 项目结构

```
src/
├── views/
│   ├── Login.vue           # 登录页面
│   ├── AdminDashboard.vue  # 管理员首页
│   └── UserDashboard.vue   # 用户首页
├── router/
│   └── index.js           # 路由配置
├── App.vue                # 根组件
└── main.js               # 入口文件
```

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 构建生产版本：
```bash
npm run build
```

## 使用说明

### 登录
- 访问 `http://localhost:5173` 会自动跳转到登录页面
- 使用用户名 `admin` 登录会进入管理员端
- 使用其他用户名登录会进入用户端
- 密码需要与后端数据库中的密码匹配

### 后端API配置
登录功能需要后端API支持，默认配置为：
- API地址：`http://localhost:8080/user/login`
- 请求方式：POST
- 参数：username, password
- 返回格式：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "userType": "admin"
  }
}
```

## 路由说明

- `/` - 重定向到登录页面
- `/login` - 登录页面
- `/admin` - 管理员首页（需要admin权限）
- `/user` - 用户首页（需要user权限）

## 权限控制

系统包含路由守卫，会自动检查：
1. 用户是否已登录
2. 用户类型是否匹配页面要求
3. 自动重定向到正确的页面

## 样式特性

- 响应式设计，支持不同屏幕尺寸
- 现代化UI设计，使用渐变色和阴影效果
- 平滑的动画过渡效果
- 悬停交互效果

## 注意事项

1. 确保后端服务已启动并运行在 `http://localhost:8080`
2. 如需修改API地址，请在 `Login.vue` 中更新fetch请求的URL
3. 用户信息存储在localStorage中，刷新页面不会丢失登录状态
