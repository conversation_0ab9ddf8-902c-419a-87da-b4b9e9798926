<template>
  <div class="register-container">
    <div class="register-box">
      <!-- 添加返回按钮 -->
      <button type="button" class="back-btn" @click="goToLogin">
        ← 返回
      </button>
      
      <div class="register-header">
        <h2>商品管理系统</h2>
        <p>请输入您账户的注册信息</p>
      </div>
      
      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            type="text"
            id="username"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input
            type="password"
            id="password"
            v-model="loginForm.password"
            placeholder="请输入密码"
            required
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input
            type="password"
            id="confirmPassword"
            v-model="loginForm.confirmPassword"
            placeholder="请再次输入密码"
            required
          />
        </div>

        <div class="form-group">
          <label for="phone">手机号</label>
          <input
            type="tel"
            id="phone"
            v-model="loginForm.phone"
            placeholder="请输入手机号"
            required
          />
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            type="email"
            id="email"
            v-model="loginForm.email"
            placeholder="请输入邮箱"
            required
          />
        </div>

        <div class="form-group">
          <label for="code">验证码</label>
          <div class="code-input-group">
            <input
              type="text"
              id="code"
              v-model="loginForm.code"
              placeholder="请输入验证码"
              required
            />
            <button 
              type="button" 
              class="send-code-btn" 
              :disabled="isCountingDown"
              @click="sendCode"
            >
              {{ isCountingDown ? `${countDown}秒后重发` : '发送验证码' }}
            </button>
          </div>
        </div>

        <div v-if="codeSentMessage" class="code-sent-message">
          已发送验证码至您的邮箱
        </div>
        
        <button type="submit" class="register-btn" :disabled="loading">
          {{ loading ? '注册中...' : '注册' }}
        </button>
      </form>
      
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const errorMessage = ref('')
const isCountingDown = ref(false)
const countDown = ref(60)
const codeSentMessage = ref(false)
const serverCode = ref('')

const loginForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  phone: '',
  code: ''
})

// 添加返回登录页面的方法
const goToLogin = () => {
  router.push('/login')
}

const sendCode = async () => {
 // 检查用户名和邮箱是否已填写
  if (!loginForm.username || !loginForm.email) {
    errorMessage.value = '请先填写用户名和邮箱'
    return
  }

  try {
    // 调用后端接口获取验证码
    const response = await fetch(`http://localhost:8083/user/getCode?username=${encodeURIComponent(loginForm.username)}&email=${encodeURIComponent(loginForm.email)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      // 保存服务端生成的验证码（实际项目中不应在前端保存，这里仅用于演示）
      serverCode.value = result.data
      
      // 显示发送成功提示
      codeSentMessage.value = true
      errorMessage.value = ''
      
      // 启动倒计时
      isCountingDown.value = true
      countDown.value = 60
  // 启动倒计时
  const timer = setInterval(() => {
    countDown.value--
    if (countDown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
      countDown.value = 60
    }
  }, 1000)
}else{
    errorMessage.value = result.message || '验证码发送失败'
  }
}catch (error) {
    console.error('验证码发送错误:', error)
    errorMessage.value = '网络错误，请稍后重试'
  }
}

const handleRegister = async () => {
    //验证密码一致
    if(loginForm.password !== loginForm.confirmPassword){
        errorMessage.value = '密码不一致，请重新输入'
        return
    }
   // 验证验证码是否填写
  if (!loginForm.code) {
    errorMessage.value = '请输入验证码'
    return
  }

  
  
  try {
    // 模拟API调用 - 实际项目中应该调用后端API
    const response = await fetch('http://localhost:8083/user/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `username=${encodeURIComponent(loginForm.username)}&password=${encodeURIComponent(loginForm.password)}&email=${encodeURIComponent(loginForm.email)}&phone=${encodeURIComponent(loginForm.phone)}`
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      // 注册成功
      alertProps('注册成功,请登录')
      router.push('/login')
    } else {
      errorMessage.value = result.message || '注册失败'
    }
  } catch (error) {
    console.error('注册错误:', error)
    errorMessage.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-box {
  background: white;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  animation: slideUp 0.6s ease-out;
  position: relative; /* 为返回按钮定位做准备 */
}

/* 添加返回按钮样式 */
.back-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  background: none;
  border: none;
  color: #667eea;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  color: #333;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: 600;
}

.register-header p {
  color: #666;
  font-size: 14px;
}

.register-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 验证码输入组样式 */
.code-input-group {
  display: flex;
  gap: 10px;
}

.code-input-group input {
  flex: 1;
}

.send-code-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 100px;
}

.send-code-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.send-code-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 验证码发送提示信息样式 */
.code-sent-message {
  color: #667eea;
  font-size: 14px;
  margin-top: -10px;
  margin-bottom: 10px;
  text-align: left;
}

.register-btn { 
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 15px;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #fcc;
  font-size: 14px;
}

.register-tips {
  text-align: center;
  margin-top: 20px;
}

.register-tips p {
  color: #888;
  font-size: 12px;
  line-height: 1.5;
}
</style>