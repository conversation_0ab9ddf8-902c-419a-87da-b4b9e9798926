import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import register from '@/views/register.vue'
import AdminDashboard from '../views/dashboard/AdminDashboard.vue'
import UserDashboard from '../views/dashboard/UserDashboard.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: Login
    },
    {
      path: '/register',
      name: 'register',
      component: register
    },
    {
      path: '/admin',
      name: 'AdminDashboard',
      component: AdminDashboard,
      meta: { requiresAuth: true, userType: 'admin' }
    },
    {
      path: '/user',
      name: 'UserDashboard',
      component: UserDashboard,
      meta: { requiresAuth: true, userType: 'user' }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userType = localStorage.getItem('userType')
  const userInfo = localStorage.getItem('userInfo')

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (!userInfo || !userType) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 检查用户类型是否匹配
    if (to.meta.userType && to.meta.userType !== userType) {
      // 用户类型不匹配，跳转到对应的页面
      if (userType === 'admin') {
        next('/admin')
      } else {
        next('/user')
      }
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到对应的首页
  if (to.path === '/login' && userInfo && userType) {
    if (userType === 'admin') {
      next('/admin')
    } else {
      next('/user')
    }
    return
  }

  next()
})

export default router
