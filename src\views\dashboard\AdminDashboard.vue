<template>
  <div class="admin-dashboard">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-left">
        <h1>商品管理系统 - 管理员端</h1>
      </div>
      <div class="header-right">
        <div class="user-info">
          <span class="welcome-text">欢迎，{{ userInfo.username }}</span>
          <button @click="logout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <nav class="nav-menu">
          <div class="menu-item active">
            <i class="icon">🏠</i>
            <span>首页概览</span>
          </div>
          <div class="menu-item">
            <i class="icon">📦</i>
            <span>商品管理</span>
          </div>
          <div class="menu-item">
            <i class="icon">📊</i>
            <span>库存管理</span>
          </div>
          <div class="menu-item">
            <i class="icon">📈</i>
            <span>销售统计</span>
          </div>
          <div class="menu-item">
            <i class="icon">👥</i>
            <span>用户管理</span>
          </div>
          <div class="menu-item">
            <i class="icon">⚙️</i>
            <span>系统设置</span>
          </div>
        </nav>
      </aside>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 数据统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
              📦
            </div>
            <div class="stat-info">
              <h3>商品总数</h3>
              <p class="stat-number">1,234</p>
              <span class="stat-change positive">+12% 较上月</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
              💰
            </div>
            <div class="stat-info">
              <h3>总销售额</h3>
              <p class="stat-number">¥89,456</p>
              <span class="stat-change positive">+8% 较上月</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
              📊
            </div>
            <div class="stat-info">
              <h3>库存预警</h3>
              <p class="stat-number">23</p>
              <span class="stat-change negative">需要补货</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
              👥
            </div>
            <div class="stat-info">
              <h3>活跃用户</h3>
              <p class="stat-number">567</p>
              <span class="stat-change positive">+15% 较上月</span>
            </div>
          </div>
        </div>

        <!-- 快速操作区域 -->
        <div class="quick-actions">
          <h2>快速操作</h2>
          <div class="action-grid">
            <div class="action-card">
              <div class="action-icon">➕</div>
              <h3>添加商品</h3>
              <p>快速添加新商品到系统</p>
              <button class="action-btn">立即添加</button>
            </div>

            <div class="action-card">
              <div class="action-icon">📋</div>
              <h3>库存盘点</h3>
              <p>进行库存盘点和调整</p>
              <button class="action-btn">开始盘点</button>
            </div>

            <div class="action-card">
              <div class="action-icon">📊</div>
              <h3>生成报表</h3>
              <p>生成销售和库存报表</p>
              <button class="action-btn">生成报表</button>
            </div>

            <div class="action-card">
              <div class="action-icon">⚙️</div>
              <h3>系统维护</h3>
              <p>系统备份和维护操作</p>
              <button class="action-btn">进入维护</button>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activity">
          <h2>最近活动</h2>
          <div class="activity-list">
            <div class="activity-item">
              <div class="activity-time">10:30</div>
              <div class="activity-content">
                <strong>张三</strong> 添加了商品 <em>"iPhone 15 Pro"</em>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-time">09:45</div>
              <div class="activity-content">
                <strong>李四</strong> 更新了库存信息
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-time">09:20</div>
              <div class="activity-content">
                系统自动备份完成
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-time">08:55</div>
              <div class="activity-content">
                <strong>王五</strong> 生成了销售报表
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userInfo = ref({})

onMounted(() => {
  // 获取用户信息
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  }
  
  // 检查是否为管理员
  const userType = localStorage.getItem('userType')
  if (userType !== 'admin') {
    router.push('/login')
  }
})

const logout = () => {
  localStorage.removeItem('userInfo')
  localStorage.removeItem('userType')
  router.push('/login')
}
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0 30px;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left h1 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.welcome-text {
  color: #666;
  font-size: 14px;
}

.logout-btn {
  padding: 8px 16px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #ff3742;
  transform: translateY(-1px);
}

.main-content {
  display: flex;
  min-height: calc(100vh - 70px);
}

.sidebar {
  width: 250px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px 0;
}

.nav-menu {
  padding: 0 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  margin-bottom: 5px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #333;
}

.menu-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.menu-item .icon {
  font-size: 18px;
}

.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-info h3 {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 12px;
}

.stat-change.positive {
  color: #27ae60;
}

.stat-change.negative {
  color: #e74c3c;
}

.quick-actions, .recent-activity {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.quick-actions h2, .recent-activity h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  text-align: center;
  padding: 20px;
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.action-card:hover {
  border-color: #667eea;
  transform: translateY(-3px);
}

.action-icon {
  font-size: 36px;
  margin-bottom: 15px;
}

.action-card h3 {
  color: #333;
  margin-bottom: 10px;
}

.action-card p {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
}

.action-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.activity-list {
  space-y: 15px;
}

.activity-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  color: #999;
  font-size: 12px;
  min-width: 50px;
}

.activity-content {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}
</style>
