<template>
  <div class="user-dashboard">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-left">
        <h1>商品管理系统 - 用户端</h1>
      </div>
      <div class="header-right">
        <div class="user-info">
          <span class="welcome-text">欢迎，{{ userInfo.username }}</span>
          <button @click="logout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <nav class="nav-menu">
          <div class="menu-item active">
            <i class="icon">🏠</i>
            <span>首页</span>
          </div>
          <div class="menu-item">
            <i class="icon">🛍️</i>
            <span>商品浏览</span>
          </div>
          <div class="menu-item">
            <i class="icon">🛒</i>
            <span>购物车</span>
          </div>
          <div class="menu-item">
            <i class="icon">📋</i>
            <span>我的订单</span>
          </div>
          <div class="menu-item">
            <i class="icon">👤</i>
            <span>个人中心</span>
          </div>
        </nav>
      </aside>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 欢迎横幅 -->
        <div class="welcome-banner">
          <div class="banner-content">
            <h2>欢迎来到商品管理系统</h2>
            <p>发现优质商品，享受便捷购物体验</p>
          </div>
          <div class="banner-image">
            🛍️
          </div>
        </div>

        <!-- 功能卡片 -->
        <div class="feature-grid">
          <div class="feature-card">
            <div class="feature-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
              🔍
            </div>
            <div class="feature-info">
              <h3>商品搜索</h3>
              <p>快速找到您需要的商品</p>
              <button class="feature-btn">立即搜索</button>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
              ⭐
            </div>
            <div class="feature-info">
              <h3>热门推荐</h3>
              <p>查看最受欢迎的商品</p>
              <button class="feature-btn">查看推荐</button>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
              🏷️
            </div>
            <div class="feature-info">
              <h3>优惠活动</h3>
              <p>不要错过限时优惠</p>
              <button class="feature-btn">查看优惠</button>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
              📦
            </div>
            <div class="feature-info">
              <h3>订单跟踪</h3>
              <p>实时查看订单状态</p>
              <button class="feature-btn">查看订单</button>
            </div>
          </div>
        </div>

        <!-- 商品分类 -->
        <div class="categories-section">
          <h2>商品分类</h2>
          <div class="categories-grid">
            <div class="category-item">
              <div class="category-icon">📱</div>
              <span>电子产品</span>
            </div>
            <div class="category-item">
              <div class="category-icon">👕</div>
              <span>服装鞋帽</span>
            </div>
            <div class="category-item">
              <div class="category-icon">🏠</div>
              <span>家居用品</span>
            </div>
            <div class="category-item">
              <div class="category-icon">📚</div>
              <span>图书文具</span>
            </div>
            <div class="category-item">
              <div class="category-icon">🎮</div>
              <span>运动娱乐</span>
            </div>
            <div class="category-item">
              <div class="category-icon">🍔</div>
              <span>食品饮料</span>
            </div>
          </div>
        </div>

        <!-- 最新商品 -->
        <div class="latest-products">
          <h2>最新商品</h2>
          <div class="products-grid">
            <div class="product-card">
              <div class="product-image">📱</div>
              <div class="product-info">
                <h4>iPhone 15 Pro</h4>
                <p class="product-price">¥8,999</p>
                <button class="add-to-cart-btn">加入购物车</button>
              </div>
            </div>

            <div class="product-card">
              <div class="product-image">💻</div>
              <div class="product-info">
                <h4>MacBook Air</h4>
                <p class="product-price">¥9,999</p>
                <button class="add-to-cart-btn">加入购物车</button>
              </div>
            </div>

            <div class="product-card">
              <div class="product-image">🎧</div>
              <div class="product-info">
                <h4>AirPods Pro</h4>
                <p class="product-price">¥1,999</p>
                <button class="add-to-cart-btn">加入购物车</button>
              </div>
            </div>

            <div class="product-card">
              <div class="product-image">⌚</div>
              <div class="product-info">
                <h4>Apple Watch</h4>
                <p class="product-price">¥2,999</p>
                <button class="add-to-cart-btn">加入购物车</button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userInfo = ref({})

onMounted(() => {
  // 获取用户信息
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  }
  
  // 检查是否为普通用户
  const userType = localStorage.getItem('userType')
  if (userType !== 'user') {
    router.push('/login')
  }
})

const logout = () => {
  localStorage.removeItem('userInfo')
  localStorage.removeItem('userType')
  router.push('/login')
}
</script>

<style scoped>
.user-dashboard {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0 30px;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left h1 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.welcome-text {
  color: #666;
  font-size: 14px;
}

.logout-btn {
  padding: 8px 16px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #ff3742;
  transform: translateY(-1px);
}

.main-content {
  display: flex;
  min-height: calc(100vh - 70px);
}

.sidebar {
  width: 250px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px 0;
}

.nav-menu {
  padding: 0 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  margin-bottom: 5px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #333;
}

.menu-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.menu-item .icon {
  font-size: 18px;
}

.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.banner-content h2 {
  font-size: 32px;
  margin-bottom: 10px;
}

.banner-content p {
  font-size: 16px;
  opacity: 0.9;
}

.banner-image {
  font-size: 80px;
  opacity: 0.3;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.feature-info h3 {
  color: #333;
  margin-bottom: 8px;
}

.feature-info p {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
}

.feature-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.feature-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.categories-section, .latest-products {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.categories-section h2, .latest-products h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item:hover {
  border-color: #667eea;
  transform: translateY(-3px);
}

.category-icon {
  font-size: 36px;
  margin-bottom: 10px;
}

.category-item span {
  color: #666;
  font-size: 14px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.product-card {
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.product-card:hover {
  border-color: #667eea;
  transform: translateY(-3px);
}

.product-image {
  font-size: 48px;
  margin-bottom: 15px;
}

.product-info h4 {
  color: #333;
  margin-bottom: 8px;
}

.product-price {
  color: #e74c3c;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
}

.add-to-cart-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(67, 233, 123, 0.3);
}
</style>
